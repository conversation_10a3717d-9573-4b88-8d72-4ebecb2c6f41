// abstracts/_spacing.scss
// Spacing variables and functions

@use "variables" as vars;

// Spacing function to get values from the spacing map
@function spacing($key) {
  @if map-has-key(vars.$spacing-map, $key) {
    @return map-get(vars.$spacing-map, $key);
  }
  @error "Unknown spacing key `#{$key}` in $spacing-map.";
}

// Common spacing variables
$page-width: vars.$pageWidth;
$side-panel-width: vars.$sidePanelWidth;
$top-spacing: vars.$topSpacing;
