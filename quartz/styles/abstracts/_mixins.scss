@use "variables" as vars;

// Layout mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin container {
  width: 100%;
  max-width: 65ch;
  margin-left: auto;
  margin-right: auto;
  padding: 0 map-get(#{vars.$spacing-map}, "md");
}

// Card style mixin used by multiple components
@mixin card-base {
  display: inline-flex;
  white-space: normal;
  padding: 0.5rem 1rem;
  background: transparent;
  transition: color #{vars.$transition-fast} ease;

  &:hover {
    color: var(--tertiary);
  }
}

// Common header styles
@mixin header-base {
  transition: all #{vars.$transition-fast} ease;
  color: var(--dark);

  &:hover {
    cursor: pointer;
    color: var(--tertiary);
  }

  &[id] > a[href^="#"] {
    opacity: 0;
    background: none;
    transition: opacity #{vars.$transition-fast} ease;
    padding: 0 0.5rem;

    &:hover {
      opacity: 1;
      color: var(--secondary);
    }
  }
}

// Callout mixins
@mixin callout-base {
  border: 1px solid var(--border);
  background: var(--bg);
  border-radius: 8px;
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  overflow: visible;
  transition: all vars.$transition-medium ease;
  box-sizing: border-box;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
  position: relative;

  // Add subtle inner glow effect
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    border-radius: 8px 8px 0 0;
  }
}

@mixin callout-content-base {
  font-size: 0.95em;
  line-height: 1.5;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;

  & > :first-child {
    margin-top: 0;
  }

  & > :last-child {
    margin-bottom: 0;
  }

  li,
  ol {
    font-size: inherit;
    color: inherit;
  }

  :not(pre) > code {
    font-size: 0.95em;
    background: transparent;
    color: var(--gray);
  }
}
