.spacer {
  flex: 1 1 auto;
}

div:has(> .overflow) {
  display: flex;
  overflow-y: auto;
  max-height: 100%;
}

ul.overflow,
ol.overflow {
  max-height: 100%;
  overflow-y: auto;

  // clearfix
  content: "";
  clear: both;

  & > li:last-of-type {
    margin-bottom: 30px;
  }
  /*&:after {
    pointer-events: none;
    content: "";
    width: 100%;
    height: 50px;
    position: absolute;
    left: 0;
    bottom: 0;
    opacity: 1;
    transition: opacity 0.3s ease;
    background: linear-gradient(transparent 0px, var(--light));
  }*/
}

.transclude {
  ul {
    padding-left: 1rem;
  }
}

.katex-display {
  overflow-x: auto;
  overflow-y: hidden;
}
