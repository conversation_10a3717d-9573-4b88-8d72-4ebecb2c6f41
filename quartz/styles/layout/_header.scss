@use "../abstracts/variables" as quartz;
@use "../abstracts/spacing";
@use "../abstracts/mixins";
@use "../abstracts/theme";

.article-title {
  text-align: center;
  margin: 1.2rem 0 0.7rem; // reduced vertical space
  font-size: 2rem; // slightly smaller
  font-weight: 700;
  letter-spacing: -0.01em;
  color: var(--header-fg);
  background: none;
  border: none;
  line-height: 1.1;
}

.content-meta {
  text-align: center;
  color: var(--gray);
  font-size: 0.95rem;
  margin-bottom: 0.7rem; // reduced
  letter-spacing: 0.01em;
  span {
    &:not(:last-child)::after {
      content: "•";
      margin: 0 0.4rem;
    }
  }
}

// Page title (site title in header)
.page-title {
  a {
    font-size: 1.7rem; // slightly smaller
    font-weight: 700;
    color: var(--header-fg);
    background: none;
    border: none;
    letter-spacing: -0.01em;
    line-height: 1.1;
    transition: color theme.$transition-fast ease;
    padding: 0.3rem 0; // reduced
    border-radius: 0;
    box-shadow: none;
    &:hover {
      color: var(--tertiary);
      background: none;
    }
  }
}
