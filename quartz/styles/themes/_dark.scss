// themes/_dark.scss
// Dark theme variables and styles

@use "../abstracts/variables" as vars;

:root[saved-theme="dark"] {
  color-scheme: dark;

  // Custom scrollbar for dark mode
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--light);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--lightgray);
    border-radius: 4px;

    &:hover {
      background: var(--gray);
    }
  }

  // Custom properties for dark mode
  --surface: transparent; // Surface color for cards, code blocks
  --radius: 0; // Border radius for elements
  --max-w: 720px; // Max width for content
  --shadow-color: transparent;
  --card-shadow: none;
  --card-border: none;

  --header-bg: #181a1b;
  --header-fg: #f8f8f8;
  --header-shadow: none;
  --header-border: none;
  --header-height: 64px;
  --header-font-weight: 700;
  --header-font-size: 2rem;
  --header-padding: 2rem 0 1.5rem 0;
  --header-letter-spacing: -0.01em;
  --header-line-height: 1.1;
  --header-radius: 0;
  --header-margin-bottom: 2.5rem;

  // Grid background
  --grid-color: rgba(255, 255, 255, 0.08);
  --grid-size: 20px;

  // Question callout
  --question-border-light: #7fdbff;
  --question-bg-light: rgba(127, 219, 255, 0.08);
  --question-border-dark: #88c0d0;
  --question-bg-dark: rgba(136, 192, 208, 0.1);

  // Note callout
  --note-border-light: #b48ead;
  --note-bg-light: rgba(180, 142, 173, 0.08);
  --note-border-dark: #81a1c1;
  --note-bg-dark: rgba(129, 161, 193, 0.1);

  // Warning callout
  --warning-border-light: #f39c12;
  --warning-bg-light: rgba(243, 156, 18, 0.08);
  --warning-border-dark: #ebcb8b;
  --warning-bg-dark: rgba(235, 203, 139, 0.1);

  // Tip callout
  --tip-border-light: #2ecc40;
  --tip-bg-light: rgba(46, 204, 64, 0.08);
  --tip-border-dark: #a3be8c;
  --tip-bg-dark: rgba(163, 190, 140, 0.1);

  // Code block variables for dark mode
  --lightgray: #23272e;
  --dark: #f8f8f2;
  --gray: #6c7680;
  --darkgray: #b0b8c1;
  --light: #181a1b;
  --codeFont: "Source Code Pro", "Fira Mono", "Menlo", "Monaco", "Consolas", monospace;
  --highlight: #3b4252;
  --secondary: #7fdbff;
  --border: #23272e;

  // Shiki syntax highlighting variables for Nord theme
  --shiki-dark: #eceff4; // Nord light text color
  --shiki-dark-bg: #2e3440; // Nord dark background
}
