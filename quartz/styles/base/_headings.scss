// base/_headings.scss
// Heading styles

@use "../abstracts/variables" as vars;

h1,
h2,
h3,
h4,
h5,
h6,
thead {
  font-family: var(--headerFont);
  color: var(--dark);
  font-weight: 400;
  margin-bottom: 0;
  letter-spacing: -0.01em;
  line-height: 1.3;

  article > & > a[role="anchor"] {
    color: var(--dark);
    background-color: transparent;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  &[id] > a[href^="#"] {
    margin: 0 0.5rem;
    opacity: 0;
    transition: opacity vars.$transition-fast ease;
    font-family: var(--codeFont);
    user-select: none;
    color: var(--gray);
    text-decoration: none;
  }

  &[id]:hover > a {
    opacity: 0.5;
  }

  &[id] > a[href^="#"]:hover {
    opacity: 0.8;
    color: var(--secondary);
  }
}

// typography improvements
h1 {
  font-size: 2.5rem;
  margin-top: 2.5rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
}

h2 {
  font-size: 1.75rem;
  margin-top: 2.25rem;
  margin-bottom: 1.25rem;
  font-weight: 600;
  padding-bottom: 0.25rem;
  border-bottom: none;
}

h3 {
  font-size: 1.4rem;
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-weight: 500;
  font-style: normal;
}

h4 {
  font-size: 1.2rem;
  margin-top: 1.75rem;
  margin-bottom: 0.8rem;
  font-weight: 500;
  font-style: normal;
}

h5,
h6 {
  font-size: 1rem;
  margin-top: 1.5rem;
  margin-bottom: 0.8rem;
  font-weight: 500;
  color: var(--gray);
}
