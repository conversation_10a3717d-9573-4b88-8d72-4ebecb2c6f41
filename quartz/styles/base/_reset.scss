// base/_reset.scss
// CSS reset/normalize

// Box sizing rules
*,
*::before,
*::after {
  box-sizing: border-box;
}

// Remove default margin
body,
h1,
h2,
h3,
h4,
p,
figure,
blockquote,
dl,
dd {
  margin: 0;
}

// Set core body defaults
body {
  min-height: 100vh;
  text-rendering: optimizeSpeed;
  line-height: 1.5;
  padding: 0;
}

// Remove list styles on ul, ol elements by default
// Content lists will be styled in components/_lists.scss
ul,
ol {
  list-style: none;
  padding: 0;
  margin: 0;
}

// Make images easier to work with
img,
picture {
  max-width: 100%;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

// Inherit fonts for inputs and buttons
input,
button,
textarea,
select {
  font: inherit;
}

// Remove all animations, transitions and smooth scroll for people that prefer not to see them
@media (prefers-reduced-motion: reduce) {
  html:focus-within {
    scroll-behavior: auto;
  }

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
