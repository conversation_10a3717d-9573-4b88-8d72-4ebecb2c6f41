// components/_buttons.scss
// Button styles

@use "../abstracts/variables" as vars;
@use "../abstracts/mixins";

// Base button style
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: vars.$semiBoldWeight;
  text-decoration: none;
  cursor: pointer;
  transition: all vars.$transition-fast ease;
  border: none;
  
  // Primary button
  &.btn-primary {
    background-color: var(--secondary);
    color: var(--light);
    
    &:hover, &:focus {
      background-color: var(--tertiary);
    }
  }
  
  // Secondary button
  &.btn-secondary {
    background-color: var(--lightgray);
    color: var(--dark);
    
    &:hover, &:focus {
      background-color: var(--gray);
    }
  }
  
  // Icon button
  &.btn-icon {
    padding: 0.5rem;
    border-radius: 50%;
    
    svg {
      width: 1.2rem;
      height: 1.2rem;
    }
  }
  
  // Disabled state
  &:disabled, &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Button groups
.btn-group {
  display: flex;
  
  .btn {
    border-radius: 0;
    
    &:first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }
    
    &:last-child {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
  }
}
