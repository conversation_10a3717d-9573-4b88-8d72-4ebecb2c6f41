// Syntax highlighting token styles for rehype-pretty-code/shiki
pre code .token.comment { color: #6a9955; }
pre code .token.keyword { color: #569cd6; }
pre code .token.string { color: #ce9178; }
pre code .token.function { color: #dcdcaa; }
pre code .token.number { color: #b5cea8; }
pre code .token.operator { color: #d4d4d4; }
pre code .token.class-name { color: #4ec9b0; }
pre code .token.constant { color: #b5cea8; }
pre code .token.tag { color: #569cd6; }
pre code .token.attr-name { color: #9cdcfe; }
pre code .token.property { color: #9cdcfe; }
pre code .token.punctuation { color: #d4d4d4; }
pre code .token.boolean { color: #569cd6; }
pre code .token.builtin { color: #dcdcaa; }
pre code .token.inserted { color: #b5cea8; }
pre code .token.deleted { color: #ce9178; }
pre code .token.selector { color: #d7ba7d; }
pre code .token.important { color: #569cd6; font-weight: bold; }
pre code .token.italic { font-style: italic; }
pre code .token.bold { font-weight: bold; }

// This file is now deprecated. All syntax highlighting styles are in base/_syntax.scss.
// Please use only base/_syntax.scss for syntax highlighting styles.
