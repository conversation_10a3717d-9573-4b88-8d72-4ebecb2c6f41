input[type="checkbox"] {
  transform: translateY(2px);
  color: var(--secondary);
  border: 1px solid var(--lightgray);
  border-radius: 3px;
  background-color: var(--light);
  position: relative;
  margin-inline-end: 0.2rem;
  margin-inline-start: -1.4rem;
  appearance: none;
  width: 16px;
  height: 16px;

  &:checked {
    border-color: var(--secondary);
    background-color: var(--secondary);

    &::after {
      content: "";
      position: absolute;
      left: 4px;
      top: 1px;
      width: 4px;
      height: 8px;
      display: block;
      border: solid var(--light);
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
    }
  }
}
