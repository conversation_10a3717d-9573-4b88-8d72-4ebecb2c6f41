// components/_toc.scss
@use "../abstracts/variables" as quartz;
@use "../abstracts/spacing";
@use "../abstracts/mixins";
@use "../abstracts/theme";

.toc {
  padding: 1.5rem;
  // background: color-mix(in srgb, var(--light) 50%, transparent);
  border-radius: 8px;
  border: 1px solid var(--lightgray);


  h3 {
    font-size: 1.1rem;
    margin-top: 0;
    margin-bottom: 1rem;
    color: var(--dark);
  }

  .overflow {
    max-height: calc(100vh - 200px);
    padding-right: 1rem;
  }

  & > #toc-content {
    margin-left: 0.25rem;
    max-height: 100%;
    overflow: hidden;
    transition:
      max-height theme.$transition-slow ease,
      visibility 0s linear 0s;
    visibility: visible;

    &.collapsed {
      max-height: 0;
      transition:
        max-height theme.$transition-medium ease,
        visibility 0s linear theme.$transition-medium;
      visibility: hidden;
    }

    a {
      color: var(--secondary);
      text-decoration: none;
      font-size: 0.7em;
      line-height: 1.4;
      padding: 0.3rem 0;
      display: block;
      transition:
        theme.$transition-slow ease opacity,
        theme.$transition-medium ease color;

      &:hover {
        color: var(--darkgray);
        opacity: 0.75;
        font-weight: 550;
        padding-left: 0.2rem;
      }

      &.in-view {
        color: var(--darkgray);
        opacity: 0.6;
      }
    }

    a > svg {
      height: 1ex;
      margin: 0 0.3em;
    }

    li {
      margin-block: 0.5em;
      // line-height: 1.25;
    }

    // Indent levels
    @for $i from 0 through 6 {
      & .depth-#{$i} {
        padding-left: calc(0.5rem * #{$i});
      }
    }
  }
}
