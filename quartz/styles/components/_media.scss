// components/_media.scss
@use "../variables.scss" as quartz;
@use "../abstracts/spacing";
@use "../abstracts/mixins";
@use "../abstracts/theme";

// Base styles for all media elements
img, video, audio {
  display: block;
  max-width: 100%;
  margin: 1.5rem auto; // Auto margins for horizontal centering
  border-radius: 0;
  box-shadow: none;
  border: none;
  transition: none;
}

// Ensure parent containers don't restrict centering
article p:has(> img), article div:has(> img), article a:has(> img) {
  text-align: center; /* Center only containers with images */
}

// Ensure images are always centered in all contexts
img, a > img, figure > img, p > img, article img, [src^="https://"] {
  display: block;
  margin-left: auto;
  margin-right: auto;
  text-align: center; /* For older browsers */
}

// Full-width PDF embeds
iframe.pdf {
  width: 100%;
  height: 100%;
  border-radius: 0;
  border: none;
  box-shadow: none;
}

// YouTube embeds
.external-embed.youtube {
  aspect-ratio: 16 / 9;
  width: 100%;
  border-radius: 0;
  margin: 1.5rem auto; // Center YouTube embeds
  box-shadow: none;
  overflow: hidden;
  border: none;
}

// Dark mode adjustments
:root[saved-theme="dark"] {
  img, video, audio {
    box-shadow: theme.$shadow-sm-dark, theme.$shadow-md-dark;

    &:hover {
      box-shadow: theme.$shadow-md-dark, theme.$shadow-lg-dark;
    }
  }
}

// Image captions
p > img + em {
  display: block;
  text-align: center;
  transform: translateY(-0.5rem);
  color: var(--gray);
  font-size: 0.9em;
  max-width: 100%;
  margin: 0 auto;
  line-height: 1.5;
}

// Buy me a coffee embed (if you use it)
.bmac-script {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 2rem auto; // Already centered
  max-width: 85%;

  iframe {
    border: none;
  }
}

// Mobile adjustments
@media all and (#{quartz.$mobile}) {
  img, video, audio {
    max-width: 95%;
    margin: 1rem auto; // Slightly reduced margins on mobile
  }

  .external-embed.youtube {
    margin: 0.75rem auto; // Center YouTube embeds on mobile
    width: 95%;
  }
}
