// components/_listPage.scss
// List page component styles

@use "../abstracts/variables" as vars;

ul.section-ul {
  list-style: none;
  margin-top: 2em;
  padding-left: 0;
}

li.section-li {
  margin-bottom: 1em;

  & > .section {
    display: grid;
    grid-template-columns: fit-content(8em) 3fr 0.1fr;

    @media all {
      & > .tags {
        display: none;
      }
    }

    & > .desc > h3 > a {
      background-color: transparent;
    }

    & .meta {
      margin: 0 1em 0 0;
      opacity: 0.6;
    }
  }
}

// modifications in popover context
.popover .section {
  grid-template-columns: fit-content(8em) 1fr !important;

  & > .tags {
    display: none;
  }
}
