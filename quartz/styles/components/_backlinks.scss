// components/_backlinks.scss
@use "../abstracts/variables" as vars;
@use "../abstracts/mixins";

.backlinks {
  margin-top: 4rem;
  padding-top: 2rem;
  border-top: 1px solid var(--lightgray);
  background: transparent;
  margin-bottom: 2rem;
  display: block;
  width: 100%;

  h3 {
    font-size: 1.15rem;
    font-weight: 700;
    color: var(--header-fg);
    margin: 0 0 1.2rem 0;
    letter-spacing: 0.01em;
    text-align: left;
    display: block;
    width: 100%;
  }

  .backlinks-container {
    display: block;
    width: 100%;
    clear: both;
  }

  .overflow {
    max-height: 300px;
    overflow-y: auto;
    padding-right: 0.5rem;

    /* Subtle scrollbar styling */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--lightgray);
      border-radius: 3px;
    }
  }

  ul {
    list-style: disc;
    column-width: 250px;
    column-gap: 2rem;
    padding: 0 0 0 1.5rem;
    margin: 0.5rem 0;

    li {
      margin: 0 0 0.5rem 0;
      padding: 0;
      break-inside: avoid;
      page-break-inside: avoid;
    }

    a {
      text-decoration: underline;
      display: inline;
      font-size: 1rem;
      color: var(--secondary);
      transition: color 0.2s;
      line-height: 1.4;

      &:hover {
        color: var(--tertiary);
      }

      .external-icon {
        display: none;
      }
    }
  }
}
