// components/_callouts.scss
// Callout component styles

@use "../abstracts/variables" as vars;
@use "../abstracts/mixins";
@use "./callout-icons";

// Main callout styles
.callout {
  @include mixins.callout-base;

  // Enhanced light mode styling
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.08),
    0 1px 3px rgba(0, 0, 0, 0.06);
  border-width: 1px;
  border-style: solid;
  background: linear-gradient(135deg, var(--bg) 0%, color-mix(in srgb, var(--bg) 98%, white) 100%);

  // Add hover effect for better interactivity
  &:hover {
    transform: translateY(-1px);
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.12),
      0 2px 6px rgba(0, 0, 0, 0.08);
  }

  // Default callout style
  &[data-callout] {
    --color: #2d3748;
    --border: #4a5568;
    --bg: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);

    .callout-icon {
      width: 18px;
      height: 18px;
      display: inline-flex;
      align-items: center;
      justify-content: center;

      &:before {
        content: "";
        width: 18px;
        height: 18px;
        display: inline-block;
        background-color: var(--border);
        mask-image: var(--callout-icon-info);
        -webkit-mask-image: var(--callout-icon-info);
        mask-size: contain;
        -webkit-mask-size: contain;
        mask-repeat: no-repeat;
        -webkit-mask-repeat: no-repeat;
        mask-position: center;
        -webkit-mask-position: center;
      }
    }
  }

  // Specific callout types
  &[data-callout="question"] {
    --color: #2c5282;
    --border: #3182ce;
    --bg: linear-gradient(135deg, rgba(49, 130, 206, 0.12) 0%, rgba(49, 130, 206, 0.06) 100%);
    border-left: 4px solid #3182ce;

    .callout-icon:before {
      background-color: #3182ce;
      mask-image: var(--callout-icon-question);
      -webkit-mask-image: var(--callout-icon-question);
    }
  }

  &[data-callout="note"] {
    --color: #553c9a;
    --border: #805ad5;
    --bg: linear-gradient(135deg, rgba(128, 90, 213, 0.12) 0%, rgba(128, 90, 213, 0.06) 100%);
    border-left: 4px solid #805ad5;

    .callout-icon:before {
      background-color: #805ad5;
      mask-image: var(--callout-icon-note);
      -webkit-mask-image: var(--callout-icon-note);
    }
  }

  &[data-callout="warning"] {
    --color: #c05621;
    --border: #dd6b20;
    --bg: linear-gradient(135deg, rgba(221, 107, 32, 0.12) 0%, rgba(221, 107, 32, 0.06) 100%);
    border-left: 4px solid #dd6b20;

    .callout-icon:before {
      background-color: #dd6b20;
      mask-image: var(--callout-icon-warning);
      -webkit-mask-image: var(--callout-icon-warning);
    }
  }

  &[data-callout="tip"] {
    --color: #276749;
    --border: #38a169;
    --bg: linear-gradient(135deg, rgba(56, 161, 105, 0.12) 0%, rgba(56, 161, 105, 0.06) 100%);
    border-left: 4px solid #38a169;

    .callout-icon:before {
      background-color: #38a169;
      mask-image: var(--callout-icon-tip);
      -webkit-mask-image: var(--callout-icon-tip);
    }
  }

  &[data-callout="abstract"],
  &[data-callout="summary"],
  &[data-callout="tldr"] {
    --color: #553c9a;
    --border: #805ad5;
    --bg: linear-gradient(135deg, rgba(128, 90, 213, 0.12) 0%, rgba(128, 90, 213, 0.06) 100%);
    border-left: 4px solid #805ad5;

    .callout-icon:before {
      background-color: #805ad5;
      mask-image: var(--callout-icon-abstract);
      -webkit-mask-image: var(--callout-icon-abstract);
    }
  }

  &[data-callout="info"] {
    --color: #2a69ac;
    --border: #3182ce;
    --bg: linear-gradient(135deg, rgba(49, 130, 206, 0.12) 0%, rgba(49, 130, 206, 0.06) 100%);
    border-left: 4px solid #3182ce;

    .callout-icon:before {
      background-color: #3182ce;
      mask-image: var(--callout-icon-info);
      -webkit-mask-image: var(--callout-icon-info);
      mask-mode: alpha;
      -webkit-mask-mode: alpha;
    }
  }

  &[data-callout="todo"] {
    --color: #553c9a;
    --border: #805ad5;
    --bg: linear-gradient(135deg, rgba(128, 90, 213, 0.12) 0%, rgba(128, 90, 213, 0.06) 100%);
    border-left: 4px solid #805ad5;

    .callout-icon:before {
      background-color: #805ad5;
      mask-image: var(--callout-icon-todo);
      -webkit-mask-image: var(--callout-icon-todo);
    }
  }

  &[data-callout="success"],
  &[data-callout="check"],
  &[data-callout="done"] {
    --color: #22543d;
    --border: #2f855a;
    --bg: linear-gradient(135deg, rgba(47, 133, 90, 0.12) 0%, rgba(47, 133, 90, 0.06) 100%);
    border-left: 4px solid #2f855a;

    .callout-icon:before {
      background-color: #2f855a;
      mask-image: var(--callout-icon-success);
      -webkit-mask-image: var(--callout-icon-success);
    }
  }

  &[data-callout="failure"],
  &[data-callout="fail"],
  &[data-callout="missing"] {
    --color: #c53030;
    --border: #e53e3e;
    --bg: linear-gradient(135deg, rgba(229, 62, 62, 0.12) 0%, rgba(229, 62, 62, 0.06) 100%);
    border-left: 4px solid #e53e3e;

    .callout-icon:before {
      background-color: #e53e3e;
      mask-image: var(--callout-icon-failure);
      -webkit-mask-image: var(--callout-icon-failure);
    }
  }

  &[data-callout="danger"],
  &[data-callout="error"] {
    --color: #9b2c2c;
    --border: #c53030;
    --bg: linear-gradient(135deg, rgba(197, 48, 48, 0.12) 0%, rgba(197, 48, 48, 0.06) 100%);
    border-left: 4px solid #c53030;

    .callout-icon:before {
      background-color: #c53030;
      mask-image: var(--callout-icon-danger);
      -webkit-mask-image: var(--callout-icon-danger);
    }
  }

  &[data-callout="bug"] {
    --color: #c05621;
    --border: #dd6b20;
    --bg: linear-gradient(135deg, rgba(221, 107, 32, 0.12) 0%, rgba(221, 107, 32, 0.06) 100%);
    border-left: 4px solid #dd6b20;

    .callout-icon:before {
      background-color: #dd6b20;
      mask-image: var(--callout-icon-bug);
      -webkit-mask-image: var(--callout-icon-bug);
    }
  }

  &[data-callout="example"] {
    --color: #234e52;
    --border: #319795;
    --bg: linear-gradient(135deg, rgba(49, 151, 149, 0.12) 0%, rgba(49, 151, 149, 0.06) 100%);
    border-left: 4px solid #319795;

    .callout-icon:before {
      background-color: #319795;
      mask-image: var(--callout-icon-example);
      -webkit-mask-image: var(--callout-icon-example);
    }
  }

  &[data-callout="quote"],
  &[data-callout="cite"] {
    --color: #4a5568;
    --border: #718096;
    --bg: linear-gradient(135deg, rgba(113, 128, 150, 0.12) 0%, rgba(113, 128, 150, 0.06) 100%);
    border-left: 4px solid #718096;

    .callout-icon:before {
      background-color: #718096;
      mask-image: var(--callout-icon-quote);
      -webkit-mask-image: var(--callout-icon-quote);
    }
  }

  // Callout content styling
  .callout-content {
    @include mixins.callout-content-base;
    color: #2d3748;

    p,
    li,
    span {
      color: #2d3748;
    }

    strong,
    b {
      color: #1a202c;
      font-weight: 600;
    }

    em,
    i {
      color: #4a5568;
    }

    code {
      background-color: rgba(0, 0, 0, 0.06);
      color: #d53f8c;
      padding: 0.2rem 0.4rem;
      border-radius: 3px;
      font-size: 0.9em;
    }

    a {
      color: var(--secondary);

      &:hover {
        color: #2c5282;
      }
    }
  }

  // Enhanced callout title styling
  .callout-title {
    .callout-title-inner > p {
      color: var(--color);
      font-weight: 600;
      text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    }
  }

  // Topics badges - special styling
  &[data-callout="topics"] {
    --color: transparent;
    --border: none;
    --bg: transparent;
    padding: 0;
    margin: 1.5rem 0;
    border: none;
    box-shadow: none;
    background-color: transparent;

    .callout-title {
      display: none;
    }

    .callout-content {
      ul {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        list-style: none;
        padding: 0.5rem 0;
        justify-content: center;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        scroll-behavior: smooth;

        &::-webkit-scrollbar {
          display: none;
        }

        li {
          margin: 0;
          padding: 0;
          display: inline-flex;
        }

        li > a.internal {
          display: inline-block;
          white-space: nowrap;
          padding: 0.35rem 0.75rem;
          font-size: 0.85rem;
          font-weight: 500;
          color: var(--secondary);
          background-color: rgba(0, 116, 217, 0.06);
          border-radius: 16px;
          text-decoration: none;
          transition: all 0.2s ease;
          border: 1px solid rgba(0, 116, 217, 0.1);

          &:hover {
            color: white;
            background-color: var(--secondary);
            transform: translateY(-1px);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
          }

          &.non-existent {
            color: var(--gray);
            background-color: rgba(128, 128, 128, 0.06);
            border: 1px solid rgba(128, 128, 128, 0.1);
            pointer-events: none;
            cursor: default;

            &:hover {
              color: var(--gray);
              background-color: rgba(128, 128, 128, 0.06);
              transform: none;
              box-shadow: none;
            }
          }
        }
      }
    }
  }

  // Collapsible functionality
  &.is-collapsible > .callout-title {
    cursor: pointer;
  }

  &.is-collapsed > .callout-title > .fold-callout-icon {
    transform: rotateZ(-90deg);
  }

  .fold-callout-icon {
    position: relative;
    width: 18px;
    height: 18px;
    opacity: 0.8;

    &:before {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 0;
      height: 0;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-top: 5px solid var(--color);
    }
  }
}

// Callout title styling
.callout-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0;
  color: var(--color);
  font-weight: 600;
  line-height: 1.3;
  min-height: auto;
  overflow: visible;

  .callout-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.25rem;

    &:before {
      content: "";
      width: 20px;
      height: 20px;
      display: inline-block;
      vertical-align: middle;
    }
  }

  .callout-title-inner {
    flex: 1 1 auto;
    display: flex;
    align-items: center;
    min-width: 0;
    overflow: visible;
    > p {
      color: var(--color);
      margin: 0;
      line-height: 1.3;
      font-size: 0.97em;
      letter-spacing: 0.01em;
      font-weight: 600;
      white-space: normal;
      word-break: break-word;
      overflow: visible;
      text-overflow: unset;
      width: 100%;
    }
  }

  .fold-callout-icon {
    margin-left: 0.25rem;
    transition: transform vars.$transition-fast ease;
    width: 18px;
    height: 18px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;

    &:before {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 0;
      height: 0;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-top: 5px solid var(--color);
    }
  }
}

// Dark mode specific adjustments
:root[saved-theme="dark"] {
  .callout {
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.3),
      0 1px 3px rgba(0, 0, 0, 0.2);
    border-width: 1px;
    border-style: solid;
    background: linear-gradient(
      135deg,
      var(--bg) 0%,
      color-mix(in srgb, var(--bg) 95%, transparent) 100%
    );

    // Add hover effect for better interactivity
    &:hover {
      transform: translateY(-1px);
      box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.4),
        0 2px 6px rgba(0, 0, 0, 0.3);
    }

    // Default callout style in dark mode
    &[data-callout] {
      --color: var(--dark);
      --border: #4a5568;
      --bg: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);

      .callout-icon:before {
        background-color: var(--border);
      }

      .fold-callout-icon:before {
        border-top-color: var(--dark);
      }
    }

    &[data-callout="question"] {
      --color: #e2e8f0;
      --border: #63b3ed;
      --bg: linear-gradient(135deg, rgba(99, 179, 237, 0.15) 0%, rgba(99, 179, 237, 0.08) 100%);
      border-left: 4px solid #63b3ed;

      .callout-icon:before {
        background-color: #63b3ed;
      }
    }

    &[data-callout="note"] {
      --color: #e2e8f0;
      --border: #9f7aea;
      --bg: linear-gradient(135deg, rgba(159, 122, 234, 0.15) 0%, rgba(159, 122, 234, 0.08) 100%);
      border-left: 4px solid #9f7aea;

      .callout-icon:before {
        background-color: #9f7aea;
      }
    }

    &[data-callout="warning"] {
      --color: #e2e8f0;
      --border: #f6ad55;
      --bg: linear-gradient(135deg, rgba(246, 173, 85, 0.15) 0%, rgba(246, 173, 85, 0.08) 100%);
      border-left: 4px solid #f6ad55;

      .callout-icon:before {
        background-color: #f6ad55;
      }
    }

    &[data-callout="tip"] {
      --color: #e2e8f0;
      --border: #68d391;
      --bg: linear-gradient(135deg, rgba(104, 211, 145, 0.15) 0%, rgba(104, 211, 145, 0.08) 100%);
      border-left: 4px solid #68d391;

      .callout-icon:before {
        background-color: #68d391;
      }
    }

    &[data-callout="abstract"],
    &[data-callout="summary"],
    &[data-callout="tldr"] {
      --color: #e2e8f0;
      --border: #9f7aea;
      --bg: linear-gradient(135deg, rgba(159, 122, 234, 0.15) 0%, rgba(159, 122, 234, 0.08) 100%);
      border-left: 4px solid #9f7aea;

      .callout-icon:before {
        background-color: #9f7aea;
      }
    }

    &[data-callout="info"] {
      --color: #e2e8f0;
      --border: #4299e1;
      --bg: linear-gradient(135deg, rgba(66, 153, 225, 0.15) 0%, rgba(66, 153, 225, 0.08) 100%);
      border-left: 4px solid #4299e1;

      .callout-icon:before {
        background-color: #4299e1;
        mask-image: var(--callout-icon-info);
        -webkit-mask-image: var(--callout-icon-info);
        mask-mode: alpha;
        -webkit-mask-mode: alpha;
      }
    }

    &[data-callout="todo"] {
      --color: #e2e8f0;
      --border: #9f7aea;
      --bg: linear-gradient(135deg, rgba(159, 122, 234, 0.15) 0%, rgba(159, 122, 234, 0.08) 100%);
      border-left: 4px solid #9f7aea;

      .callout-icon:before {
        background-color: #9f7aea;
      }
    }

    &[data-callout="success"],
    &[data-callout="check"],
    &[data-callout="done"] {
      --color: #e2e8f0;
      --border: #48bb78;
      --bg: linear-gradient(135deg, rgba(72, 187, 120, 0.15) 0%, rgba(72, 187, 120, 0.08) 100%);
      border-left: 4px solid #48bb78;

      .callout-icon:before {
        background-color: #48bb78;
      }
    }

    &[data-callout="failure"],
    &[data-callout="fail"],
    &[data-callout="missing"] {
      --color: #e2e8f0;
      --border: #f56565;
      --bg: linear-gradient(135deg, rgba(245, 101, 101, 0.15) 0%, rgba(245, 101, 101, 0.08) 100%);
      border-left: 4px solid #f56565;

      .callout-icon:before {
        background-color: #f56565;
      }
    }

    &[data-callout="danger"],
    &[data-callout="error"] {
      --color: #e2e8f0;
      --border: #e53e3e;
      --bg: linear-gradient(135deg, rgba(229, 62, 62, 0.15) 0%, rgba(229, 62, 62, 0.08) 100%);
      border-left: 4px solid #e53e3e;

      .callout-icon:before {
        background-color: #e53e3e;
      }
    }

    &[data-callout="bug"] {
      --color: #e2e8f0;
      --border: #ed8936;
      --bg: linear-gradient(135deg, rgba(237, 137, 54, 0.15) 0%, rgba(237, 137, 54, 0.08) 100%);
      border-left: 4px solid #ed8936;

      .callout-icon:before {
        background-color: #ed8936;
      }
    }

    &[data-callout="example"] {
      --color: #e2e8f0;
      --border: #38b2ac;
      --bg: linear-gradient(135deg, rgba(56, 178, 172, 0.15) 0%, rgba(56, 178, 172, 0.08) 100%);
      border-left: 4px solid #38b2ac;

      .callout-icon:before {
        background-color: #38b2ac;
      }
    }

    &[data-callout="quote"],
    &[data-callout="cite"] {
      --color: #e2e8f0;
      --border: #a0aec0;
      --bg: linear-gradient(135deg, rgba(160, 174, 192, 0.15) 0%, rgba(160, 174, 192, 0.08) 100%);
      border-left: 4px solid #a0aec0;

      .callout-icon:before {
        background-color: #a0aec0;
      }
    }

    // Dark mode topics badges
    &[data-callout="topics"] .callout-content ul li > a.internal {
      color: var(--secondary);
      background-color: rgba(127, 219, 255, 0.06);
      border: 1px solid rgba(127, 219, 255, 0.12);

      &:hover {
        color: var(--dark);
        background-color: var(--secondary);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
      }

      &.non-existent {
        color: var(--gray);
        background-color: rgba(128, 128, 128, 0.06);
        border: 1px solid rgba(128, 128, 128, 0.1);
        pointer-events: none;
        cursor: default;

        &:hover {
          color: var(--gray);
          background-color: rgba(128, 128, 128, 0.06);
          transform: none;
          box-shadow: none;
        }
      }
    }

    // Improve callout content in dark mode
    .callout-content {
      color: #e2e8f0;

      p,
      li,
      span {
        color: #e2e8f0;
      }

      strong,
      b {
        color: #f7fafc;
        font-weight: 600;
      }

      em,
      i {
        color: #cbd5e0;
      }

      code {
        background-color: rgba(0, 0, 0, 0.3);
        color: #fbb6ce;
        padding: 0.2rem 0.4rem;
        border-radius: 3px;
        font-size: 0.9em;
      }

      a {
        color: var(--secondary);

        &:hover {
          color: #90cdf4;
        }
      }
    }

    // Enhanced callout title styling
    .callout-title {
      .callout-title-inner > p {
        color: var(--color);
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }
    }
  }
}
