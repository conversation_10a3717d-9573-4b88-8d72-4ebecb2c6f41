// components/_popover.scss
@use "../abstracts/variables" as vars;

@keyframes dropin {
  0% {
    opacity: 0;
    visibility: hidden;
  }
  1% {
    opacity: 0;
  }
  100% {
    opacity: 1;
    visibility: visible;
  }
}

.popover {
  z-index: 999;
  position: absolute;
  overflow: visible;
  padding: 1rem;

  & > .popover-inner {
    position: relative;
    width: 30rem;
    max-height: 20rem;
    padding: 0 1rem 1rem 1rem;
    font-weight: initial;
    font-style: initial;
    line-height: normal;
    font-size: initial;
    font-family: var(--bodyFont);

    /*
    border: 1px solid var(--lightgray);   
    box-shadow: 6px 6px 36px 0 rgba(0, 0, 0, 0.25); */

    background-color: var(--light);
    border-radius: 5px;

    overflow: auto;
    white-space: normal;
  }

  & > .popover-inner[data-content-type] {
    &[data-content-type*="pdf"],
    &[data-content-type*="image"] {
      padding: 0;
      max-height: 100%;
    }

    &[data-content-type*="image"] {
      img {
        margin: 0;
        border-radius: 0;
        display: block;
      }
    }

    &[data-content-type*="pdf"] {
      iframe {
        width: 100%;
      }
    }
  }

  & > .popover-inner a,
  & > .popover-inner a:visited,
  & > .popover-inner a:active,
  & > .popover-inner a:hover {
    color: var(--secondary) !important;
    text-decoration: underline;
    background: none;
  }

  & > .popover-inner ul,
  & > .popover-inner ol,
  & > .popover-inner li {
    color: var(--dark) !important;
  }

  h1 {
    font-size: 1.5rem;
  }

  visibility: hidden;
  opacity: 0;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;

  @media all and (#{vars.$mobile}) {
    display: none !important;
  }
}

a:hover .popover,
.popover:hover {
  animation: dropin 0.3s ease;
  animation-fill-mode: forwards;
  animation-delay: 0.2s;
}
