main {
  /* Comment text colors */
  --color-fg-default: #384b70;
  --color-fg-muted: #788896;
  --color-fg-subtle: #2a394f;

  /* Background colors */
  --color-canvas-default: #fff;
  --color-canvas-overlay: #f6f8fa;
  --color-canvas-inset: #f8f9fb;
  --color-canvas-subtle: #f5f7fa;

  /* Borders */
  --color-border-default: #e0e0e0;
  --color-border-muted: rgba(224, 224, 224, 0.8);

  /* Button colors */
  --color-btn-text: #384b70;
  --color-btn-bg: #fff;
  --color-btn-border: #e0e0e0;
  --color-btn-shadow: 0 1px 2px rgba(42, 57, 79, 0.05);
  --color-btn-inset-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25);
  --color-btn-hover-bg: #f6f8fa;
  --color-btn-hover-border: #14b8a6;
  --color-btn-active-bg: #f5f7fa;
  --color-btn-active-border: #14b8a6;
  --color-btn-selected-bg: rgba(0, 116, 217, 0.07);

  /* Primary button */
  --color-btn-primary-text: #fff;
  --color-btn-primary-bg: #14b8a6;
  --color-btn-primary-border: transparent;
  --color-btn-primary-shadow: 0 1px 2px rgba(42, 57, 79, 0.1);
  --color-btn-primary-hover-bg: #0d9488;
  --color-btn-primary-hover-border: transparent;
  --color-btn-primary-selected-bg: #0d9488;
  --color-btn-primary-disabled-bg: rgba(80, 118, 135, 0.6);
  --color-btn-primary-disabled-text: rgba(255, 255, 255, 0.5);

  /* Accents and states */
  --color-accent-fg: #14b8a6;
  --color-accent-emphasis: #0d9488;
  --color-accent-muted: rgba(20, 184, 166, 0.4);
  --color-accent-subtle: rgba(20, 184, 166, 0.15);

  font-family:
    "Inter",
    "Merriweather",
    "Source Code Pro",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Helvetica,
    Arial,
    sans-serif;
}

.gsc-timeline {
  flex-direction: column-reverse;
}

.gsc-comments {
  font-family: "Nunito Sans", sans-serif;
  color: var(--color-fg-default);
}

.gsc-comments > .gsc-header {
  order: 1;
}

.gsc-comments > .gsc-comment-box {
  order: 2;
  margin-bottom: 1rem;
}

.gsc-comments > .gsc-timeline {
  order: 3;
}

.gsc-loading-image {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 24 24'%3E%3Cpath fill='rgba(184, 0, 31, 0.15)' d='M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z'%3E%3CanimateTransform attributeName='transform' dur='0.75s' repeatCount='indefinite' type='rotate' values='0 12 12;360 12 12'/%3E%3C/path%3E%3C/svg%3E");
}
